# Source files (TypeScript)
src/
*.ts
!dist/**/*.d.ts

# Development files
.github/
scripts/
tests/
docs/
*.test.js
*.spec.js

# Build files
tsconfig.json
jest.config.js
nodemon.json
renovate.json

# Docker files (not needed for npm)
Dockerfile*
docker-compose*.yml
docker/
.dockerignore

# Environment and config files
.env
.env.*
!.env.example

# IDE and OS files
.vscode/
.idea/
*.swp
.DS_Store

# Logs and temp files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.pid
*.seed
*.pid.lock

# Coverage and test reports
coverage/
.nyc_output/

# Git files
.git/
.gitignore

# Documentation source files
*.md
!README.md
!LICENSE

# Package files we don't want to publish
package-lock.json
yarn.lock
pnpm-lock.yaml

# Backup files
*.backup
*.bak

# Keep only necessary runtime files
!dist/
!data/nodes.db
!package.json
!package.runtime.json