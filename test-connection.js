const axios = require('axios');
require('dotenv').config();

async function testN8nConnection() {
    console.log('🔄 Testando conexão com n8n...');
    console.log('URL:', process.env.N8N_API_URL);
    
    try {
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            },
            timeout: 10000
        });
        
        console.log('✅ Conexão bem-sucedida!');
        console.log(`📊 Encontrados ${response.data.data?.length || 0} workflows`);
        
        if (response.data.data && response.data.data.length > 0) {
            console.log('\n📋 Primeiros workflows:');
            response.data.data.slice(0, 3).forEach((workflow, index) => {
                console.log(`  ${index + 1}. ${workflow.name} (ID: ${workflow.id})`);
            });
        }
        
        return true;
    } catch (error) {
        console.log('❌ Erro na conexão:');
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Mensagem: ${error.response.data?.message || error.response.statusText}`);
        } else if (error.request) {
            console.log('   Erro de rede - não foi possível conectar');
        } else {
            console.log(`   Erro: ${error.message}`);
        }
        return false;
    }
}

testN8nConnection().then(success => {
    if (success) {
        console.log('\n🎉 Configuração está funcionando perfeitamente!');
        console.log('🚀 O servidor MCP pode se conectar com sua instância n8n.');
    } else {
        console.log('\n⚠️  Verifique as configurações da API.');
    }
    process.exit(success ? 0 : 1);
});
