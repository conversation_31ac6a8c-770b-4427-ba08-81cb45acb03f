const axios = require('axios');
require('dotenv').config();

async function listAllWorkflows() {
    console.log('🔍 Conectando ao n8n...\n');
    
    try {
        // Buscar todos os workflows
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        const workflows = response.data.data;
        console.log(`✅ Total de workflows: ${workflows.length}\n`);
        console.log('=' .repeat(80));
        
        // Listar workflows com detalhes
        for (let i = 0; i < workflows.length; i++) {
            const w = workflows[i];
            
            // Buscar detalhes completos
            const detailResponse = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows/${w.id}`, {
                headers: {
                    'X-N8N-API-KEY': process.env.N8N_API_KEY
                }
            });
            
            const details = detailResponse.data;
            const nodeCount = details.nodes ? details.nodes.length : 0;
            
            console.log(`📋 ${i + 1}. ${w.name}`);
            console.log(`   🆔 ID: ${w.id}`);
            console.log(`   🔴 Status: ${w.active ? '✅ ATIVO' : '⚪ INATIVO'}`);
            console.log(`   🔧 Nodes: ${nodeCount}`);
            console.log(`   📅 Criado: ${new Date(w.createdAt).toLocaleString('pt-BR')}`);
            console.log(`   🔄 Atualizado: ${new Date(w.updatedAt).toLocaleString('pt-BR')}`);
            
            if (nodeCount > 0) {
                console.log(`   📌 Tipos de nodes:`);
                const nodeTypes = [...new Set(details.nodes.map(n => n.type))];
                nodeTypes.forEach(type => {
                    const count = details.nodes.filter(n => n.type === type).length;
                    console.log(`      - ${type} (${count}x)`);
                });
            }
            
            console.log('-' .repeat(80));
        }
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
        if (error.response) {
            console.error('Detalhes:', error.response.data);
        }
    }
}

// Executar
listAllWorkflows();
