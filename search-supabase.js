#!/usr/bin/env node

const { N8NDocumentationMCPServer } = require('./dist/mcp/server');

async function searchSupabaseNode() {
    console.log('🔍 Buscando informações sobre o node do Supabase...\n');
    
    try {
        const server = new N8NDocumentationMCPServer();
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 1. Buscar nodes do Supabase
        console.log('📋 Buscando nodes do Supabase...');
        const searchResult = await server.executeTool('search_nodes', {
            query: 'supabase'
        });
        
        if (searchResult.success && searchResult.data.nodes.length > 0) {
            console.log(`✅ Encontrados ${searchResult.data.nodes.length} nodes relacionados ao Supabase:\n`);
            
            searchResult.data.nodes.forEach((node, index) => {
                console.log(`${index + 1}. ${node.displayName} (${node.nodeType})`);
                console.log(`   📝 ${node.description}`);
                console.log(`   📦 Package: ${node.packageName}`);
                console.log('');
            });
            
            // 2. Pegar detalhes essenciais do primeiro node Supabase
            const supabaseNode = searchResult.data.nodes[0];
            console.log(`🔧 Buscando configurações essenciais para: ${supabaseNode.nodeType}`);
            console.log('=' .repeat(60));
            
            const essentialsResult = await server.executeTool('get_node_essentials', {
                nodeType: supabaseNode.nodeType
            });
            
            if (essentialsResult.success) {
                const essentials = essentialsResult.data;
                
                console.log(`📊 CONFIGURAÇÕES ESSENCIAIS - ${essentials.displayName}:`);
                console.log('-'.repeat(40));
                console.log(`📝 Descrição: ${essentials.description}`);
                
                if (essentials.operations && essentials.operations.length > 0) {
                    console.log(`\n⚙️ OPERAÇÕES DISPONÍVEIS (${essentials.operations.length}):`);
                    essentials.operations.forEach((op, index) => {
                        console.log(`  ${index + 1}. ${op.name} - ${op.description}`);
                    });
                }
                
                if (essentials.properties && essentials.properties.length > 0) {
                    console.log(`\n🔧 PROPRIEDADES PRINCIPAIS (${essentials.properties.length}):`);
                    essentials.properties.forEach((prop, index) => {
                        console.log(`  ${index + 1}. ${prop.displayName} (${prop.name})`);
                        console.log(`     📝 ${prop.description}`);
                        console.log(`     🔧 Tipo: ${prop.type}`);
                        if (prop.required) console.log(`     ⚠️ Obrigatório`);
                        if (prop.default !== undefined) console.log(`     🔹 Padrão: ${prop.default}`);
                        console.log('');
                    });
                }
                
                if (essentials.examples && essentials.examples.length > 0) {
                    console.log(`\n💡 EXEMPLOS DE USO:`);
                    essentials.examples.forEach((example, index) => {
                        console.log(`  ${index + 1}. ${example.name}`);
                        console.log(`     📝 ${example.description}`);
                        if (example.configuration) {
                            console.log(`     ⚙️ Configuração:`);
                            console.log(JSON.stringify(example.configuration, null, 6));
                        }
                        console.log('');
                    });
                }
                
            } else {
                console.log('❌ Erro ao buscar configurações essenciais:', essentialsResult.error);
            }
            
        } else {
            console.log('❌ Nenhum node do Supabase encontrado.');
            
            // Tentar buscar por termos relacionados
            console.log('\n🔍 Tentando buscar por termos relacionados...');
            const altSearchResult = await server.executeTool('search_nodes', {
                query: 'database postgres sql'
            });
            
            if (altSearchResult.success && altSearchResult.data.nodes.length > 0) {
                console.log(`📋 Encontrados ${altSearchResult.data.nodes.length} nodes de banco de dados:`);
                altSearchResult.data.nodes.slice(0, 5).forEach((node, index) => {
                    console.log(`  ${index + 1}. ${node.displayName} (${node.nodeType})`);
                });
            }
        }
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
    }
}

searchSupabaseNode();
