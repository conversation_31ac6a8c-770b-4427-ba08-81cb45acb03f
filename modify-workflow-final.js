const axios = require('axios');
require('dotenv').config();
const { v4: uuidv4 } = require('uuid');

async function addSupabaseLogic() {
    const WORKFLOW_ID = '8Uovo6WF0uTUvUrr';
    
    try {
        // 1. Buscar workflow atual
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows/${WORKFLOW_ID}`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        const workflow = response.data;
        console.log('✅ Workflow carregado!');
        
        // 2. Encontrar nodes importantes
        const webhookNode = workflow.nodes.find(n => n.type === 'n8n-nodes-base.webhook');
        const supabaseNode = workflow.nodes.find(n => n.type === 'n8n-nodes-base.supabase');
        const switchNode = workflow.nodes.find(n => n.name === 'Verifica tipo da mensagem');
        
        if (!supabaseNode || !switchNode) {
            console.error('❌ Nodes necessários não encontrados!');
            return;
        }
        
        // 3. Gerar IDs únicos para novos nodes
        const checkUserId = uuidv4();
        const insertUserId = uuidv4();
        
        // 4. Criar node IF para verificar se existe usuário
        const checkUserNode = {
            "parameters": {
                "conditions": {
                    "options": {
                        "caseSensitive": true,
                        "leftValue": "",
                        "typeValidation": "strict"
                    },
                    "conditions": [
                        {
                            "id": "d5f3c8b5-2c2f-4b85-b9f9-9c2e8f9d5a3e",
                            "leftValue": "={{ $json.length }}",
                            "rightValue": 0,
                            "operator": {
                                "type": "number",
                                "operation": "gt"
                            }
                        }
                    ],
                    "combinator": "and"
                },
                "options": {}
            },
            "id": checkUserId,
            "name": "Usuário Existe?",
            "type": "n8n-nodes-base.if",
            "typeVersion": 2.1,
            "position": [supabaseNode.position[0] + 200, supabaseNode.position[1]]
        };
        
        // 5. Criar node para inserir novo usuário
        const insertUserNode = {
            "parameters": {
                "operation": "create",
                "tableId": "noivas",
                "options": {},
                "fieldsUi": {
                    "fieldValues": [
                        {
                            "fieldName": "telefone",
                            "fieldValue": "={{ $('Webhook').item.json.body.data.remoteJid.split('@')[0] }}"
                        },
                        {
                            "fieldName": "nome", 
                            "fieldValue": "={{ $('Webhook').item.json.body.data.pushName || 'Não informado' }}"
                        },
                        {
                            "fieldName": "data_cadastro",
                            "fieldValue": "={{ new Date().toISOString() }}"
                        }
                    ]
                }
            },
            "id": insertUserId,
            "name": "Cadastrar Novo Usuário",
            "type": "n8n-nodes-base.supabase",
            "typeVersion": 1,
            "position": [supabaseNode.position[0] + 200, supabaseNode.position[1] + 200],
            "credentials": supabaseNode.credentials
        };
        
        // 6. Modificar o node Supabase original
        const supabaseIndex = workflow.nodes.findIndex(n => n.id === supabaseNode.id);
        workflow.nodes[supabaseIndex] = {
            ...supabaseNode,
            "name": "Buscar Usuário",
            "parameters": {
                "operation": "getAll",
                "tableId": "noivas",
                "returnAll": false,
                "limit": 1,
                "filters": {
                    "conditions": [
                        {
                            "keyName": "telefone",
                            "condition": "eq",
                            "keyValue": "={{ $json.body.data.remoteJid.split('@')[0] }}"
                        }
                    ]
                },
                "options": {}
            }
        };
        
        // 7. Adicionar novos nodes
        workflow.nodes.push(checkUserNode);
        workflow.nodes.push(insertUserNode);
        
        // 8. Atualizar conexões
        // Remover conexão antiga do Webhook para Supabase
        delete workflow.connections[webhookNode.id];
        
        // Webhook → Supabase (buscar usuário)
        workflow.connections[webhookNode.id] = {
            "main": [[{
                "node": supabaseNode.id,
                "type": "main",
                "index": 0
            }]]
        };
        
        // Supabase → IF (verificar se existe)
        workflow.connections[supabaseNode.id] = {
            "main": [[{
                "node": checkUserId,
                "type": "main",
                "index": 0
            }]]
        };
        
        // IF → Switch (se existe) ou Insert (se não existe)
        workflow.connections[checkUserId] = {
            "main": [
                // Output 0 (true - usuário existe)
                [{
                    "node": switchNode.id,
                    "type": "main",
                    "index": 0
                }],
                // Output 1 (false - usuário não existe)
                [{
                    "node": insertUserId,
                    "type": "main",
                    "index": 0
                }]
            ]
        };
        
        // Insert → Switch (após cadastrar)
        workflow.connections[insertUserId] = {
            "main": [[{
                "node": switchNode.id,
                "type": "main",
                "index": 0
            }]]
        };
        
        // 9. Preparar dados para update
        const updateData = {
            name: workflow.name,
            nodes: workflow.nodes,
            connections: workflow.connections,
            active: workflow.active,
            settings: workflow.settings || {},
            staticData: workflow.staticData || null,
            tags: workflow.tags || []
        };
        
        // 10. Atualizar workflow
        console.log('\n📝 Enviando workflow modificado...');
        
        const updateResponse = await axios.put(
            `${process.env.N8N_API_URL}/api/v1/workflows/${WORKFLOW_ID}`,
            updateData,
            {
                headers: {
                    'X-N8N-API-KEY': process.env.N8N_API_KEY,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        if (updateResponse.status === 200) {
            console.log('✅ Workflow atualizado com sucesso!');
            console.log('\n🎯 Modificações realizadas:');
            console.log('1. ✓ Node Supabase renomeado para "Buscar Usuário"');
            console.log('2. ✓ Configurado para buscar por telefone do WhatsApp');
            console.log('3. ✓ Adicionado node "Usuário Existe?" para verificação');
            console.log('4. ✓ Adicionado node "Cadastrar Novo Usuário"');
            console.log('\n📊 Novo fluxo:');
            console.log('Webhook → Buscar Usuário → Existe? → SIM: Continua fluxo');
            console.log('                                   → NÃO: Cadastra → Continua fluxo');
            console.log('\n💡 Campos cadastrados para novos usuários:');
            console.log('- telefone (número do WhatsApp)');
            console.log('- nome (nome do contato ou "Não informado")');
            console.log('- data_cadastro (timestamp atual)');
        }
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Detalhes:', JSON.stringify(error.response.data, null, 2));
        }
    }
}

// Verificar se uuid está instalado
try {
    require('uuid');
    addSupabaseLogic();
} catch (e) {
    console.log('📦 Instalando dependência uuid...');
    require('child_process').execSync('npm install uuid', { stdio: 'inherit' });
    console.log('✅ Dependência instalada! Execute o script novamente.');
}
