const axios = require('axios');
require('dotenv').config();

async function findAndAnalyzeWorkflow(workflowName) {
    console.log(`🔍 Buscando workflow: "${workflowName}"`);
    
    try {
        // Buscar todos os workflows
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        // Encontrar o workflow específico
        const workflow = response.data.data.find(w => 
            w.name.toLowerCase().includes(workflowName.toLowerCase())
        );
        
        if (!workflow) {
            console.log(`❌ Workflow "${workflowName}" não encontrado.`);
            console.log('\n📋 Workflows disponíveis:');
            response.data.data.forEach((w, index) => {
                console.log(`  ${index + 1}. ${w.name} (ID: ${w.id})`);
            });
            return;
        }
        
        console.log(`✅ Workflow encontrado: "${workflow.name}" (ID: ${workflow.id})`);
        
        // Buscar detalhes completos do workflow
        const detailResponse = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows/${workflow.id}`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        const workflowDetails = detailResponse.data;
        
        console.log('\n📊 ANÁLISE DO WORKFLOW:');
        console.log('=' .repeat(50));
        console.log(`📝 Nome: ${workflowDetails.name}`);
        console.log(`🆔 ID: ${workflowDetails.id}`);
        console.log(`📅 Criado: ${new Date(workflowDetails.createdAt).toLocaleString('pt-BR')}`);
        console.log(`🔄 Atualizado: ${new Date(workflowDetails.updatedAt).toLocaleString('pt-BR')}`);
        console.log(`🔴 Ativo: ${workflowDetails.active ? 'Sim' : 'Não'}`);
        
        // Analisar nodes
        const nodes = workflowDetails.nodes || [];
        console.log(`\n🔧 NODES (${nodes.length} total):`);
        console.log('-'.repeat(30));
        
        const nodeTypes = {};
        nodes.forEach(node => {
            const type = node.type;
            nodeTypes[type] = (nodeTypes[type] || 0) + 1;
            console.log(`  • ${node.name} (${type})`);
        });
        
        console.log(`\n📈 RESUMO POR TIPO:`);
        console.log('-'.repeat(30));
        Object.entries(nodeTypes).forEach(([type, count]) => {
            console.log(`  ${type}: ${count}x`);
        });
        
        // Analisar conexões
        const connections = workflowDetails.connections || {};
        const connectionCount = Object.keys(connections).length;
        console.log(`\n🔗 CONEXÕES: ${connectionCount} nodes conectados`);
        
        // Verificar triggers
        const triggers = nodes.filter(node => 
            node.type.includes('Trigger') || 
            node.type.includes('Webhook') ||
            node.type === 'manualTrigger'
        );
        
        console.log(`\n🎯 TRIGGERS (${triggers.length}):`);
        triggers.forEach(trigger => {
            console.log(`  • ${trigger.name} (${trigger.type})`);
        });
        
        // Salvar detalhes completos para análise
        const fs = require('fs');
        const filename = `workflow-${workflow.id}-details.json`;
        fs.writeFileSync(filename, JSON.stringify(workflowDetails, null, 2));
        console.log(`\n💾 Detalhes salvos em: ${filename}`);
        
        return workflowDetails;
        
    } catch (error) {
        console.log('❌ Erro ao buscar workflow:');
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Mensagem: ${error.response.data?.message || error.response.statusText}`);
        } else {
            console.log(`   Erro: ${error.message}`);
        }
    }
}

// Executar análise
const workflowName = process.argv[2] || 'Atendimento Casamento';
findAndAnalyzeWorkflow(workflowName);
