# .env.docker
# Docker-specific environment template
# Copy to .env and fill in values

# Required for HTTP mode
AUTH_TOKEN=

# Server configuration
PORT=3000
HTTP_PORT=80
HTTPS_PORT=443

# Application settings
NODE_ENV=production
LOG_LEVEL=info
MCP_MODE=http

# Database
NODE_DB_PATH=/app/data/nodes.db
REBUILD_ON_START=false

# Optional nginx mode
USE_NGINX=false

# Optional n8n API configuration (enables 16 additional management tools)
# N8N_API_URL=https://your-n8n-instance.com
# N8N_API_KEY=your-api-key-here
# N8N_API_TIMEOUT=30000
# N8N_API_MAX_RETRIES=3