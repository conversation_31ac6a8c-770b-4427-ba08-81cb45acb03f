const axios = require('axios');
require('dotenv').config();

async function modifySupabaseNode() {
    const WORKFLOW_ID = '8Uovo6WF0uTUvUrr';
    
    try {
        // 1. Buscar workflow atual
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows/${WORKFLOW_ID}`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        const workflow = response.data;
        console.log('✅ Workflow carregado!');
        
        // 2. Encontrar índice do node Supabase
        const supabaseNodeIndex = workflow.nodes.findIndex(node => 
            node.type === 'n8n-nodes-base.supabase' && node.name === 'Get many rows'
        );
        
        if (supabaseNodeIndex === -1) {
            console.error('❌ Node Supabase não encontrado!');
            return;
        }
        
        const supabaseNode = workflow.nodes[supabaseNodeIndex];
        console.log(`🔧 Modificando node: ${supabaseNode.name}`);
        
        // 3. Criar novo node IF para verificar se existe usuário
        const checkUserNode = {
            "id": "check-user-exists",
            "name": "Usuário Existe?",
            "type": "n8n-nodes-base.if",
            "typeVersion": 2,
            "position": [supabaseNode.position[0] + 200, supabaseNode.position[1]],
            "parameters": {
                "conditions": {
                    "options": {
                        "caseSensitive": true,
                        "leftValue": "",
                        "typeValidation": "strict"
                    },
                    "conditions": [
                        {
                            "id": "condition1",
                            "leftValue": "={{ $json.length }}",
                            "rightValue": 0,
                            "operator": {
                                "type": "number",
                                "operation": "gt"
                            }
                        }
                    ],
                    "combinator": "and"
                },
                "options": {}
            }
        };
        
        // 4. Criar node para inserir novo usuário
        const insertUserNode = {
            "id": "insert-new-user",
            "name": "Cadastrar Novo Usuário",
            "type": "n8n-nodes-base.supabase",
            "typeVersion": 1,
            "position": [supabaseNode.position[0] + 400, supabaseNode.position[1] + 150],
            "parameters": {
                "operation": "create",
                "tableId": "noivas",
                "fieldsUi": {
                    "fieldValues": [
                        {
                            "fieldName": "telefone",
                            "fieldValue": "={{ $('Webhook').item.json.body.data.remoteJid.split('@')[0] }}"
                        },
                        {
                            "fieldName": "nome",
                            "fieldValue": "={{ $('Webhook').item.json.body.data.pushName || 'Não informado' }}"
                        },
                        {
                            "fieldName": "data_cadastro",
                            "fieldValue": "={{ new Date().toISOString() }}"
                        },
                        {
                            "fieldName": "primeira_mensagem",
                            "fieldValue": "={{ $('Webhook').item.json.body.data.message.conversation || $('Webhook').item.json.body.data.message.extendedTextMessage?.text || 'Mensagem de mídia' }}"
                        }
                    ]
                }
            },
            "credentials": {
                "supabaseApi": {
                    "id": supabaseNode.credentials?.supabaseApi?.id || "1",
                    "name": supabaseNode.credentials?.supabaseApi?.name || "Supabase account"
                }
            }
        };
        
        // 5. Modificar o node Supabase original para buscar por telefone
        workflow.nodes[supabaseNodeIndex] = {
            ...supabaseNode,
            "name": "Buscar Usuário",
            "parameters": {
                "operation": "getAll",
                "tableId": "noivas",
                "filters": {
                    "conditions": [
                        {
                            "keyName": "telefone",
                            "condition": "eq",
                            "keyValue": "={{ $('Webhook').item.json.body.data.remoteJid.split('@')[0] }}"
                        }
                    ]
                },
                "options": {
                    "limit": 1
                }
            }
        };
        
        // 6. Adicionar os novos nodes ao workflow
        workflow.nodes.push(checkUserNode);
        workflow.nodes.push(insertUserNode);
        
        // 7. Atualizar conexões
        // Remover conexão antiga do Supabase para Switch
        const oldConnection = workflow.connections[supabaseNode.id];
        
        // Criar novas conexões
        workflow.connections[supabaseNode.id] = {
            "main": [
                [{
                    "node": checkUserNode.id,
                    "type": "main",
                    "index": 0
                }]
            ]
        };
        
        workflow.connections[checkUserNode.id] = {
            "main": [
                // True (usuário existe) - vai direto pro switch
                [{
                    "node": "Verifica tipo da mensagem",
                    "type": "main",
                    "index": 0
                }],
                // False (usuário não existe) - cadastra primeiro
                [{
                    "node": insertUserNode.id,
                    "type": "main",
                    "index": 0
                }]
            ]
        };
        
        workflow.connections[insertUserNode.id] = {
            "main": [
                [{
                    "node": "Verifica tipo da mensagem",
                    "type": "main",
                    "index": 0
                }]
            ]
        };
        
        // 8. Criar node de merge para unir os fluxos (opcional)
        const mergeNode = {
            "id": "merge-flows",
            "name": "Unir Fluxos",
            "type": "n8n-nodes-base.merge",
            "typeVersion": 3,
            "position": [supabaseNode.position[0] + 600, supabaseNode.position[1]],
            "parameters": {
                "mode": "combine",
                "combinationMode": "multiplex",
                "options": {}
            }
        };
        
        // 9. Salvar workflow modificado
        console.log('\n📝 Salvando workflow modificado...');
        
        const updateResponse = await axios.put(
            `${process.env.N8N_API_URL}/api/v1/workflows/${WORKFLOW_ID}`,
            workflow,
            {
                headers: {
                    'X-N8N-API-KEY': process.env.N8N_API_KEY,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        if (updateResponse.status === 200) {
            console.log('✅ Workflow atualizado com sucesso!');
            console.log('\n🎯 Modificações realizadas:');
            console.log('1. Node Supabase agora busca usuário pelo telefone');
            console.log('2. Adicionado node IF para verificar se usuário existe');
            console.log('3. Adicionado node para cadastrar novo usuário');
            console.log('4. Fluxo: Webhook → Buscar → IF → (Existe? Continua : Cadastra) → Switch');
        }
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
        if (error.response) {
            console.error('Detalhes:', error.response.data);
        }
    }
}

// Executar
modifySupabaseNode();
