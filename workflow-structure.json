{"createdAt": "2025-07-13T17:05:35.208Z", "updatedAt": "2025-07-13T19:34:21.029Z", "id": "8Uovo6WF0uTUvUrr", "name": "Atendimento Casamento", "active": true, "isArchived": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "802555a1-d1c8-40d6-b2af-c63631bf37bf", "options": {}}, "id": "2c79e56f-28a8-410c-b14a-9a6f844696c2", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1060, 760], "webhookId": "802555a1-d1c8-40d6-b2af-c63631bf37bf"}, {"parameters": {"assignments": {"assignments": [{"id": "c5e7478e-d8f9-4db0-adb1-4500234d9284", "name": "nome", "value": "={{ $('Webhook').item.json.body.data.pushName }}", "type": "string"}, {"id": "9b1cc3e9-bd02-495e-a3e1-ebdb93846e53", "name": "numero_ou_grupo", "value": "={{ $('Webhook').item.json.body.data.key.remoteJid }}", "type": "string"}, {"id": "282567fd-1c5c-4ed5-909b-84ff531a9ef7", "name": "mensagem", "value": "={{ $('Webhook').item.json.body.data.message.conversation }}", "type": "string"}]}, "options": {}}, "id": "5c28f489-025d-4396-8095-29932353cc5b", "name": "Informações Msg 1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1000, 320]}, {"parameters": {"assignments": {"assignments": [{"id": "c5e7478e-d8f9-4db0-adb1-4500234d9284", "name": "nome", "value": "={{ $('Webhook').item.json.body.data.pushName }}", "type": "string"}, {"id": "9b1cc3e9-bd02-495e-a3e1-ebdb93846e53", "name": "numero_ou_grupo", "value": "={{ $('Webhook').item.json.body.data.key.remoteJid }}", "type": "string"}, {"id": "04ef9bec-822a-483b-aed6-f8aef3077a2e", "name": "mensagem", "value": "={{ $('Webhook').item.json.body.data.message.conversation }}", "type": "string"}]}, "options": {}}, "id": "52cbe20b-e175-4ea9-a998-2624877da3c1", "name": "Informações Msg 2", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [680, 260]}, {"parameters": {"assignments": {"assignments": [{"id": "ae214a46-1690-4fa2-b0f4-e9e6fb5e738e", "name": "nome", "value": "={{ $json.body.data.pushName }}", "type": "string"}, {"id": "651a556a-8c64-4a02-9d33-89df4800c90a", "name": "numero_ou_grupo", "value": "={{ $json.body.data.key.remoteJid }}", "type": "string"}, {"id": "548dbae7-ffd5-4767-b651-b572acc0018b", "name": "mensagem", "value": "={{ $json.body.data.message.imageMessage.caption || ''}}", "type": "string"}, {"id": "02d4d183-8774-479b-b97b-c5df81039a7c", "name": "url_imagem", "value": "={{ $json.body.data.message.imageMessage.url }}", "type": "string"}]}, "options": {}}, "id": "27233810-acf2-49f3-8b0d-e6974f2ce64a", "name": "Image Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1060, 520]}, {"parameters": {"assignments": {"assignments": [{"id": "ae214a46-1690-4fa2-b0f4-e9e6fb5e738e", "name": "nome", "value": "={{ $('Webhook').item.json.body.data.pushName }}", "type": "string"}, {"id": "c61e3ab5-e172-494f-b427-83add8ca5437", "name": "numero_ou_grupo", "value": "={{ $('Webhook').item.json.body.data.key.remoteJid }}", "type": "string"}, {"id": "548fc402-483b-4890-ae0a-0f39220a7a36", "name": "url_audio", "value": "={{ $json.body.data.message.audioMessage.url }}", "type": "string"}, {"id": "85e7023b-4b5e-424b-9855-4df8b39b8e9f", "name": "base64", "value": "={{ $json.body.data.message.base64 }}", "type": "string"}]}, "options": {}}, "id": "2c4a7213-22cc-4e11-ac5b-07d9dcf6555b", "name": "Audio Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1080, 720]}, {"parameters": {"assignments": {"assignments": [{"id": "ae214a46-1690-4fa2-b0f4-e9e6fb5e738e", "name": "nome", "value": "={{ $json.body.instance }}", "type": "string"}, {"id": "85cbab73-5b2d-4453-b979-40526e302f83", "name": "numero_ou_grupo", "value": "={{ $json.body.data.key.remoteJid }}", "type": "string"}, {"id": "cde29830-4c6d-454d-8239-5dd7ac620a81", "name": "url_video", "value": "={{ $json.body.data.message.videoMessage.url }}", "type": "string"}, {"id": "60422335-66ed-40ca-8c86-23ab195b9371", "name": "mensagem", "value": "={{ $json.body.data.message.videoMessage.caption || ''}}", "type": "string"}]}, "options": {}}, "id": "6d2e66f8-239f-45b2-bdd2-58d73ca3294a", "name": "Video Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1060, 920]}, {"parameters": {"assignments": {"assignments": [{"id": "ae214a46-1690-4fa2-b0f4-e9e6fb5e738e", "name": "nome", "value": "={{ $json.body.data.pushName }}", "type": "string"}, {"id": "0596ecfb-3fa5-4828-968a-219ec5bd8ec3", "name": "numero_ou_grupo", "value": "={{ $json.body.data.key.remoteJid }}", "type": "string"}, {"id": "ea8d6b51-d53b-4d50-b598-09f5fdb805b8", "name": "url_documento", "value": "={{ $json.body.data.message.documentMessage.url }}", "type": "string"}, {"id": "31852fcc-ac18-476e-9100-74f3b7f9a5b1", "name": "base64", "value": "={{ $json.body.data.message.base64 }}", "type": "string"}, {"id": "b4696e60-25e7-4166-87aa-a16dc4e807f8", "name": "tipo_documento", "value": "={{ $json.body.data.message.documentMessage.mimetype.split('/')[1] }}", "type": "string"}]}, "options": {}}, "id": "37ed6692-b4ea-4ab8-b8c9-a76198a8ae11", "name": "Document Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1040, 1140]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $json.body.data.messageType }}", "rightValue": "conversation", "operator": {"type": "string", "operation": "equals"}, "id": "084e42a4-69df-4f7e-8c50-48fcce16fa30"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Texto"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1354f7a1-2a2d-4d70-8c25-b4e82c2b35b7", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "extendedTextMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Msg text 02"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "0fbdd2f3-a95f-45bd-a3a0-8cd2fb7053cf", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "imageMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "imagem"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "310639ed-229d-46a7-a10c-7d8f7f7f5c25", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "audioMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "8741e9af-36eb-426d-b7ec-0ab76060e0bd", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "videoMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "video"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "99f213c2-0502-49f8-8324-d37c15a83868", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "documentMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "documento"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "636425b8-2a25-42a5-b026-92dd4b5c802e", "leftValue": "={{ $json.body.data.messageType }}", "rightValue": "documentWithCaptionMessage", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "documento e titulo"}]}, "options": {}}, "id": "93fca009-446c-45cf-a3c4-ddb78a7b8358", "name": "Verifica tipo da mensagem", "type": "n8n-nodes-base.switch", "typeVersion": 3.1, "position": [580, 660]}, {"parameters": {"content": "# Recebimento da Mensagem e Classificação por Tipo\n\n## Recebe a Mensagem da Instancia Evolution API e separa por:\n\n- Áudio\n- Imagem\n- Texto\n- Documento", "height": 1543.6766623207304, "width": 1091.4717426298976, "color": 7}, "id": "7ed3e0a4-7229-4871-b9b9-44812d520134", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [220, 0]}, {"parameters": {"assignments": {"assignments": [{"id": "ae214a46-1690-4fa2-b0f4-e9e6fb5e738e", "name": "nome", "value": "={{ $json.body.data.pushName }}", "type": "string"}, {"id": "0596ecfb-3fa5-4828-968a-219ec5bd8ec3", "name": "numero_ou_grupo", "value": "={{ $json.body.data.key.remoteJid }}", "type": "string"}, {"id": "fe656324-4aba-4a11-9fe4-516190d6d76a", "name": "mensagem", "value": "={{ $json.body.data.message.documentWithCaptionMessage.message.documentMessage.caption }}", "type": "string"}, {"id": "ea8d6b51-d53b-4d50-b598-09f5fdb805b8", "name": "url_documento", "value": "={{ $json.body.data.message.documentWithCaptionMessage.message.documentMessage.url }}", "type": "string"}, {"id": "31852fcc-ac18-476e-9100-74f3b7f9a5b1", "name": "base64", "value": "={{ $json.body.data.message.base64 }}", "type": "string"}, {"id": "ea196b98-0a6e-46c2-9fd7-581ab1c40446", "name": "tipo_documento", "value": "={{ $json.body.data.message.documentWithCaptionMessage.message.documentMessage.mimetype.split('/')[1] }}", "type": "string"}]}, "options": {}}, "id": "cb016109-1610-4d52-b849-d58b9be26811", "name": "Document Data Com Titulo", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [680, 1260]}, {"parameters": {"operation": "getAll", "tableId": "noivas", "filters": {"conditions": [{"keyName": "telefone", "condition": "eq", "keyValue": "telefone"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-840, 760], "id": "2a0a0277-470c-4c2b-b34d-5f9d150160ff", "name": "Get many rows", "credentials": {"supabaseApi": {"id": "nZnQJ8PdZFbXPF5q", "name": "Supabase Casamento"}}}], "connections": {"Webhook": {"main": [[{"node": "Get many rows", "type": "main", "index": 0}]]}, "Verifica tipo da mensagem": {"main": [[{"node": "Informações Msg 1", "type": "main", "index": 0}], [{"node": "Informações Msg 2", "type": "main", "index": 0}], [{"node": "Image Data", "type": "main", "index": 0}], [{"node": "Audio Data", "type": "main", "index": 0}], [{"node": "Video Data", "type": "main", "index": 0}], [{"node": "Document Data", "type": "main", "index": 0}], [{"node": "Document Data Com Titulo", "type": "main", "index": 0}]]}, "Get many rows": {"main": [[{"node": "Verifica tipo da mensagem", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "8e928470-cd4e-4888-a3d7-073a6e2c7c1b", "triggerCount": 1, "shared": [{"createdAt": "2025-07-13T17:05:35.208Z", "updatedAt": "2025-07-13T17:05:35.208Z", "role": "workflow:owner", "workflowId": "8Uovo6WF0uTUvUrr", "projectId": "OGdV7Ghas0zcE6EQ", "project": {"createdAt": "2025-06-25T23:25:02.928Z", "updatedAt": "2025-06-25T23:25:41.221Z", "id": "OGdV7Ghas0zcE6EQ", "name": "<PERSON> <<EMAIL>>", "type": "personal", "icon": null, "description": null, "projectRelations": [{"createdAt": "2025-06-25T23:25:02.928Z", "updatedAt": "2025-06-25T23:25:02.928Z", "role": "project:personal<PERSON><PERSON>er", "userId": "2f9dd92b-243d-4e03-b1b4-cb79183789a6", "projectId": "OGdV7Ghas0zcE6EQ", "user": {"createdAt": "2025-06-25T23:25:02.273Z", "updatedAt": "2025-07-11T04:02:42.725Z", "id": "2f9dd92b-243d-4e03-b1b4-cb79183789a6", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-06-25T23:25:44.584Z", "personalization_survey_n8n_version": "1.99.1"}, "settings": {"userActivated": true, "firstSuccessfulWorkflowId": "d2OHomgOQbTENbV4", "userActivatedAt": 1752206562661, "easyAIWorkflowOnboarded": true, "npsSurvey": {"waitingForResponse": true, "ignoredCount": 1, "lastShownAt": 1751337598126}, "dismissedCallouts": {"aiAgentStarterCallout": true}}, "role": "global:owner", "disabled": false, "mfaEnabled": false, "isPending": false}}]}}], "tags": []}