{"name": "n8n-mcp", "version": "2.7.13", "description": "Integration between n8n workflow automation and Model Context Protocol (MCP)", "main": "dist/index.js", "bin": {"n8n-mcp": "./dist/mcp/index.js"}, "scripts": {"build": "tsc", "rebuild": "node dist/scripts/rebuild.js", "rebuild:optimized": "node dist/scripts/rebuild-optimized.js", "validate": "node dist/scripts/validate.js", "test-nodes": "node dist/scripts/test-nodes.js", "start": "node dist/mcp/index.js", "start:http": "MCP_MODE=http node dist/mcp/index.js", "start:http:fixed": "MCP_MODE=http USE_FIXED_HTTP=true node dist/mcp/index.js", "http": "npm run build && npm run start:http:fixed", "dev": "npm run build && npm run rebuild && npm run validate", "dev:http": "MCP_MODE=http nodemon --watch src --ext ts --exec 'npm run build && npm run start:http'", "test:single-session": "./scripts/test-single-session.sh", "test": "jest", "lint": "tsc --noEmit", "typecheck": "tsc --noEmit", "update:n8n": "node scripts/update-n8n-deps.js", "update:n8n:check": "node scripts/update-n8n-deps.js --dry-run", "fetch:templates": "node dist/scripts/fetch-templates.js", "fetch:templates:robust": "node dist/scripts/fetch-templates-robust.js", "prebuild:fts5": "npx tsx scripts/prebuild-fts5.ts", "test:templates": "node dist/scripts/test-templates.js", "test:workflow-validation": "node dist/scripts/test-workflow-validation.js", "test:template-validation": "node dist/scripts/test-template-validation.js", "test:essentials": "node dist/scripts/test-essentials.js", "test:enhanced-validation": "node dist/scripts/test-enhanced-validation.js", "test:ai-workflow-validation": "node dist/scripts/test-ai-workflow-validation.js", "test:mcp-tools": "node dist/scripts/test-mcp-tools.js", "test:n8n-manager": "node dist/scripts/test-n8n-manager-integration.js", "test:n8n-validate-workflow": "node dist/scripts/test-n8n-validate-workflow.js", "test:typeversion-validation": "node dist/scripts/test-typeversion-validation.js", "test:error-handling": "node dist/scripts/test-error-handling-validation.js", "test:workflow-diff": "node dist/scripts/test-workflow-diff.js", "test:transactional-diff": "node dist/scripts/test-transactional-diff.js", "test:tools-documentation": "node dist/scripts/test-tools-documentation.js", "test:search-improvements": "node dist/scripts/test-search-improvements.js", "test:fts5-search": "node dist/scripts/test-fts5-search.js", "migrate:fts5": "node dist/scripts/migrate-nodes-fts.js", "test:mcp:update-partial": "node dist/scripts/test-mcp-n8n-update-partial.js", "test:update-partial:debug": "node dist/scripts/test-update-partial-debug.js", "test:auth-logging": "tsx scripts/test-auth-logging.ts", "sanitize:templates": "node dist/scripts/sanitize-templates.js", "db:rebuild": "node dist/scripts/rebuild-database.js", "db:init": "node -e \"new (require('./dist/services/sqlite-storage-service').SQLiteStorageService)(); console.log('Database initialized')\"", "docs:rebuild": "ts-node src/scripts/rebuild-database.ts", "sync:runtime-version": "node scripts/sync-runtime-version.js", "prepare:publish": "./scripts/publish-npm.sh"}, "repository": {"type": "git", "url": "git+https://github.com/c<PERSON><PERSON><PERSON>/n8n-mcp.git"}, "keywords": ["n8n", "mcp", "model-context-protocol", "ai", "workflow", "automation"], "author": "<PERSON><PERSON><PERSON><PERSON> @ www.aiadvisors.pl/en", "license": "MIT", "bugs": {"url": "https://github.com/cz<PERSON><PERSON>/n8n-mcp/issues"}, "homepage": "https://github.com/cz<PERSON><PERSON>/n8n-mcp#readme", "files": ["dist/**/*", "data/nodes.db", ".env.example", "README.md", "LICENSE", "package.runtime.json"], "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/node": "^22.15.30", "@types/ws": "^8.18.1", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "@n8n/n8n-nodes-langchain": "^1.100.1", "axios": "^1.10.0", "better-sqlite3": "^11.10.0", "dotenv": "^16.5.0", "express": "^5.1.0", "n8n": "^1.101.1", "n8n-core": "^1.100.0", "n8n-workflow": "^1.98.0", "sql.js": "^1.13.0", "uuid": "^10.0.0"}, "optionalDependencies": {"better-sqlite3": "^11.10.0"}}