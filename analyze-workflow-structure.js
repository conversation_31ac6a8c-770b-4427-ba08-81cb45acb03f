const axios = require('axios');
require('dotenv').config();
const fs = require('fs');

async function modifySupabaseNodeCorrect() {
    const WORKFLOW_ID = '8Uovo6WF0uTUvUrr';
    
    try {
        // 1. Buscar workflow atual
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows/${WORKFLOW_ID}`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        const workflow = response.data;
        console.log('✅ Workflow carregado!');
        
        // Salvar estrutura original para análise
        fs.writeFileSync('./workflow-structure.json', JSON.stringify(workflow, null, 2));
        console.log('📋 Estrutura salva em workflow-structure.json');
        
        // 2. Encontrar o node "Verifica tipo da mensagem"
        const switchNode = workflow.nodes.find(node => 
            node.name === "Verifica tipo da mensagem"
        );
        
        console.log('\n🔍 Analisando estrutura do workflow...');
        console.log('Nodes encontrados:');
        workflow.nodes.forEach(node => {
            console.log(`- ${node.name} (${node.type})`);
        });
        
        // 3. Mostrar estrutura de conexões atual
        console.log('\n🔗 Conexões atuais:');
        Object.keys(workflow.connections).forEach(nodeId => {
            const node = workflow.nodes.find(n => n.id === nodeId);
            if (node) {
                console.log(`\n${node.name}:`);
                if (workflow.connections[nodeId].main) {
                    workflow.connections[nodeId].main.forEach((outputs, index) => {
                        outputs.forEach(connection => {
                            const targetNode = workflow.nodes.find(n => n.id === connection.node);
                            console.log(`  Output ${index} → ${targetNode?.name || connection.node}`);
                        });
                    });
                }
            }
        });
        
        // 4. Mostrar campos necessários para update
        console.log('\n📝 Preparando update com campos corretos...');
        
        // Criar objeto de update apenas com campos permitidos
        const updateData = {
            name: workflow.name,
            nodes: workflow.nodes,
            connections: workflow.connections,
            active: workflow.active,
            settings: workflow.settings || {},
            staticData: workflow.staticData || null,
            tags: workflow.tags || []
        };
        
        console.log('\nCampos do update:', Object.keys(updateData));
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Detalhes:', error.response.data);
        }
    }
}

// Executar
modifySupabaseNodeCorrect();
