# n8n Documentation MCP Server Configuration

# ====================
# COMMON CONFIGURATION
# ====================

# Database Configuration
# For local development: ./data/nodes.db
# For Docker: /app/data/nodes.db
NODE_DB_PATH=./data/nodes.db

# Logging Level (debug, info, warn, error)
MCP_LOG_LEVEL=info

# Node Environment (development, production)
NODE_ENV=development

# Rebuild database on startup (true/false)
REBUILD_ON_START=false

# =========================
# LOCAL MODE CONFIGURATION
# =========================
# Used when running: npm run start:v2 or npm run dev:v2

# Local MCP Server Configuration
MCP_SERVER_PORT=3000
MCP_SERVER_HOST=localhost
# MCP_AUTH_TOKEN=optional-for-local-development

# =========================
# SIMPLE HTTP MODE
# =========================
# Used for private single-user deployments

# Server mode: stdio (local) or http (remote)
MCP_MODE=stdio

# Use fixed HTTP implementation (recommended for stability)
# Set to true to bypass StreamableHTTPServerTransport issues
USE_FIXED_HTTP=true

# HTTP Server Configuration (only used when MCP_MODE=http)
PORT=3000
HOST=0.0.0.0

# Authentication token for HTTP mode (REQUIRED)
# Generate with: openssl rand -base64 32
AUTH_TOKEN=your-secure-token-here

# CORS origin for HTTP mode (optional)
# Default: * (allow all origins)
# For production, set to your specific domain
# CORS_ORIGIN=https://your-client-domain.com

# Trust proxy configuration for correct IP logging (0=disabled, 1=trust first proxy)
# Set to 1 when running behind a reverse proxy (Nginx, Traefik, etc.)
# Set to the number of proxy hops if behind multiple proxies
# Default: 0 (disabled)
# TRUST_PROXY=0

# =========================
# N8N API CONFIGURATION
# =========================
# Optional: Enable n8n management tools by providing API credentials
# These tools allow creating, updating, and executing workflows

# n8n instance API URL (without /api/v1 suffix)
# Example: https://your-n8n-instance.com
# N8N_API_URL=

# n8n API Key (get from Settings > API in your n8n instance)
# N8N_API_KEY=

# n8n API request timeout in milliseconds (default: 30000)
# N8N_API_TIMEOUT=30000

# Maximum number of API request retries (default: 3)
# N8N_API_MAX_RETRIES=3