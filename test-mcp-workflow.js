#!/usr/bin/env node

const { N8NDocumentationMCPServer } = require('./dist/mcp/server');

async function testMCPWorkflowTools() {
    console.log('🔍 Buscando workflow específico do Supabase...\n');
    
    try {
        // Criar instância do servidor MCP
        const server = new N8NDocumentationMCPServer();
        
        // Aguardar inicialização
        await new Promise(resolve => setTimeout(resolve, 500));

        // Buscar workflow específico pelo ID
        console.log('📋 Buscando workflow ID: 8Uovo6WF0uTUvUrr...');
        const detailsResult = await server.executeTool('n8n_get_workflow_details', {
            id: '8Uovo6WF0uTUvUrr'
        });
        
        console.log('📋 Listando todos os workflows...');
        console.log('=' .repeat(50));
        
        // Listar workflows usando a ferramenta MCP
        const result = await server.executeTool('n8n_list_workflows', {
            limit: 100
        });
        
        if (result.success) {
            const workflows = result.data.workflows;
            console.log(`✅ Encontrados ${workflows.length} workflows:\n`);
            
            // Procurar workflow "Atendimento Casamento"
            const targetWorkflow = workflows.find(w => 
                w.name.toLowerCase().includes('atendimento') && 
                w.name.toLowerCase().includes('casamento')
            );
            
            if (targetWorkflow) {
                console.log('🎯 WORKFLOW ENCONTRADO:');
                console.log('-'.repeat(30));
                console.log(`📝 Nome: ${targetWorkflow.name}`);
                console.log(`🆔 ID: ${targetWorkflow.id}`);
                console.log(`🔴 Ativo: ${targetWorkflow.active ? 'Sim' : 'Não'}`);
                console.log(`📅 Criado: ${new Date(targetWorkflow.createdAt).toLocaleString('pt-BR')}`);
                console.log(`🔄 Atualizado: ${new Date(targetWorkflow.updatedAt).toLocaleString('pt-BR')}`);
                
                // Buscar detalhes completos do workflow
                console.log('\n🔍 Buscando detalhes completos...');
                const detailsResult = await server.executeTool('n8n_get_workflow_details', {
                    id: targetWorkflow.id
                });
                
                if (detailsResult.success) {
                    const details = detailsResult.data;
                    console.log('\n📊 DETALHES DO WORKFLOW:');
                    console.log('-'.repeat(30));
                    console.log(`🔧 Nodes: ${details.nodes?.length || 0}`);
                    console.log(`🔗 Conexões: ${Object.keys(details.connections || {}).length}`);
                    
                    if (details.nodes && details.nodes.length > 0) {
                        console.log('\n🔧 LISTA DE NODES:');
                        details.nodes.forEach((node, index) => {
                            console.log(`  ${index + 1}. ${node.name} (${node.type})`);
                        });
                    }
                    
                    // Analisar tipos de nodes
                    if (details.nodes) {
                        const nodeTypes = {};
                        details.nodes.forEach(node => {
                            const type = node.type;
                            nodeTypes[type] = (nodeTypes[type] || 0) + 1;
                        });
                        
                        console.log('\n📈 RESUMO POR TIPO:');
                        Object.entries(nodeTypes).forEach(([type, count]) => {
                            console.log(`  • ${type}: ${count}x`);
                        });
                    }
                    
                    // Verificar triggers
                    if (details.nodes) {
                        const triggers = details.nodes.filter(node => 
                            node.type.includes('Trigger') || 
                            node.type.includes('Webhook') ||
                            node.type === 'manualTrigger'
                        );
                        
                        console.log(`\n🎯 TRIGGERS (${triggers.length}):`);
                        triggers.forEach(trigger => {
                            console.log(`  • ${trigger.name} (${trigger.type})`);
                        });
                    }
                    
                } else {
                    console.log('❌ Erro ao buscar detalhes:', detailsResult.error);
                }
                
            } else {
                console.log('❌ Workflow "Atendimento Casamento" não encontrado.');
                console.log('\n📋 Workflows disponíveis:');
                workflows.slice(0, 10).forEach((w, index) => {
                    console.log(`  ${index + 1}. ${w.name} (ID: ${w.id})`);
                });
                if (workflows.length > 10) {
                    console.log(`  ... e mais ${workflows.length - 10} workflows`);
                }
            }
            
        } else {
            console.log('❌ Erro ao listar workflows:', result.error);
        }
        
    } catch (error) {
        console.error('❌ Erro:', error.message);
    }
}

// Executar teste
testMCPWorkflowTools();
