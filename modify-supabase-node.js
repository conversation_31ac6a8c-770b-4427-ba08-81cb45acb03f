const axios = require('axios');
require('dotenv').config();

async function modifySupabaseNode() {
    const WORKFLOW_ID = '8Uovo6WF0uTUvUrr';
    
    try {
        // 1. Get current workflow
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows/${WORKFLOW_ID}`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        const workflow = response.data;
        
        // 2. Find Supabase node (assuming it's the 11th node based on previous output)
        const supabaseNode = workflow.nodes[10]; // 11th node (0-based index)
        
        if (!supabaseNode || supabaseNode.type !== 'n8n-nodes-base.supabase') {
            console.error('Supabase node not found in expected position');
            return;
        }

        // 3. Modify Supabase node to check for existing user
        supabaseNode.parameters = {
            operation: "getAll",
            tableId: "noivas",
            filters: {
                conditions: [{
                    keyName: "telefone",
                    condition: "eq", 
                    keyValue: "={{ $('Webhook').item.json.body.data.remoteJid.split('@')[0] }}"
                }]
            },
            options: {
                limit: 1
            }
        };

        // 4. Add IF node to check if user exists
        const ifNode = {
            id: "check-user-exists",
            name: "Usuário existe?",
            type: "n8n-nodes-base.if",
            typeVersion: 2,
            position: [supabaseNode.position[0] + 300, supabaseNode.position[1]],
            parameters: {
                conditions: {
                    conditions: [{
                        id: "condition1",
                        leftValue: "={{ $json.length }}",
                        rightValue: 0,
                        operator: {
                            type: "number",
                            operation: "gt"
                        }
                    }],
                    combinator: "and"
                }
            }
        };

        // 5. Add node to insert new user if doesn't exist
        const insertNode = {
            id: "insert-new-user",
            name: "Cadastrar usuário",
            type: "n8n-nodes-base.supabase",
            typeVersion: 1,
            position: [supabaseNode.position[0] + 300, supabaseNode.position[1] + 200],
            parameters: {
                operation: "create",
                tableId: "noivas",
                fieldsUi: {
                    fieldValues: [
                        {
                            fieldName: "telefone",
                            fieldValue: "={{ $('Webhook').item.json.body.data.remoteJid.split('@')[0] }}"
                        },
                        {
                            fieldName: "nome",
                            fieldValue: "={{ $('Webhook').item.json.body.data.pushName || 'Não informado' }}"
                        },
                        {
                            fieldName: "data_cadastro",
                            fieldValue: "={{ new Date().toISOString() }}"
                        }
                    ]
                }
            },
            credentials: supabaseNode.credentials // Reuse same credentials
        };

        // 6. Update connections
        workflow.connections[supabaseNode.id] = {
            "main": [[{
                node: ifNode.id,
                type: "main",
                index: 0
            }]]
        };

        workflow.connections[ifNode.id] = {
            "main": [
                // If user exists (true branch)
                [[{
                    node: "Verifica tipo da mensagem", // Original switch node
                    type: "main",
                    index: 0
                }]],
                // If user doesn't exist (false branch)
                [[{
                    node: insertNode.id,
                    type: "main",
                    index: 0
                }]]
            ]
        };

        workflow.connections[insertNode.id] = {
            "main": [[{
                node: "Verifica tipo da mensagem",
                type: "main",
                index: 0
            }]]
        };

        // Debug: Mostrar estrutura completa do workflow
        console.log('🔍 Estrutura completa do workflow:');
        console.log(JSON.stringify(workflow, null, 2));

        // Skip modifications in read-only mode
        if (process.argv.includes('--read-only')) {
            console.log('\nℹ️ Modo read-only - Nenhuma modificação será feita');
            return;
        }

        // 7. Add new nodes to workflow
        workflow.nodes.push(ifNode);
        workflow.nodes.push(insertNode);

        // 8. Prepare minimal update payload
        const updatePayload = {
            nodes: workflow.nodes,
            connections: workflow.connections,
            settings: workflow.settings || {},
            name: workflow.name
        };

        // 9. Save updated workflow
        const updateResponse = await axios.put(
            `${process.env.N8N_API_URL}/api/v1/workflows/${WORKFLOW_ID}`,
            updatePayload,
            {
                headers: {
                    'X-N8N-API-KEY': process.env.N8N_API_KEY,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        // Verificar nodes do tipo Supabase
        const supabaseNodes = workflow.nodes.filter(n => n.type === 'n8n-nodes-base.supabase');
        console.log(`\n✅ Encontrados ${supabaseNodes.length} nodes Supabase:`);
        supabaseNodes.forEach((node, i) => {
            console.log(`\n📌 Node ${i+1}: ${node.name}`);
            console.log('Parâmetros:', JSON.stringify(node.parameters, null, 2));
        });

        if (supabaseNodes.length === 0) {
            console.log('\n⚠️ Nenhum node Supabase encontrado no workflow!');
            console.log('Nodes disponíveis:', workflow.nodes.map(n => n.name).join(', '));
        }

    } catch (error) {
        console.error('❌ Erro:', error.message);
        if (error.response) {
            console.error('Detalhes:', error.response.data);
        }
    }
}

modifySupabaseNode();
