const axios = require('axios');
require('dotenv').config();

async function getWorkflowDetails(workflowId) {
    console.log(`🔍 Buscando detalhes do workflow: ${workflowId}\n`);
    
    try {
        const response = await axios.get(`${process.env.N8N_API_URL}/api/v1/workflows/${workflowId}`, {
            headers: {
                'X-N8N-API-KEY': process.env.N8N_API_KEY
            }
        });
        
        return response.data;
    } catch (error) {
        console.error('❌ Erro:', error.message);
        return null;
    }
}

async function updateWorkflow(workflowId, workflowData) {
    console.log(`📝 Atualizando workflow: ${workflowId}\n`);
    
    try {
        const response = await axios.put(`${process.env.N8N_API_URL}/api/v1/workflows/${workflowId}`, 
            workflowData,
            {
                headers: {
                    'X-N8N-API-KEY': process.env.N8N_API_KEY,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        return response.data;
    } catch (error) {
        console.error('❌ Erro ao atualizar:', error.message);
        if (error.response) {
            console.error('Detalhes:', error.response.data);
        }
        return null;
    }
}

// ID do workflow "Atendimento Casamento"
const WORKFLOW_ID = '8Uovo6WF0uTUvUrr';

async function analyzeAndModifyWorkflow() {
    // 1. Buscar workflow atual
    const workflow = await getWorkflowDetails(WORKFLOW_ID);
    if (!workflow) return;
    
    console.log('✅ Workflow encontrado!');
    console.log(`📊 Nodes atuais: ${workflow.nodes.length}\n`);
    
    // 2. Encontrar o node Supabase (segundo node)
    const supabaseNode = workflow.nodes.find(node => node.type === 'n8n-nodes-base.supabase');
    
    if (!supabaseNode) {
        console.error('❌ Node Supabase não encontrado!');
        return;
    }
    
    console.log(`🔧 Node Supabase encontrado: ${supabaseNode.name} (ID: ${supabaseNode.id})`);
    console.log('📋 Configuração atual:', JSON.stringify(supabaseNode.parameters, null, 2));
    
    // 3. Salvar workflow atual como backup
    const fs = require('fs');
    const backupPath = `./backup-atendimento-casamento-${Date.now()}.json`;
    fs.writeFileSync(backupPath, JSON.stringify(workflow, null, 2));
    console.log(`\n💾 Backup salvo em: ${backupPath}`);
    
    // 4. Exibir estrutura para análise
    console.log('\n📌 Estrutura dos nodes:');
    workflow.nodes.forEach((node, index) => {
        console.log(`${index + 1}. ${node.name} (${node.type})`);
    });
    
    console.log('\n🔗 Conexões:');
    workflow.connections && Object.keys(workflow.connections).forEach(nodeId => {
        const node = workflow.nodes.find(n => n.id === nodeId);
        console.log(`- ${node?.name || nodeId}:`);
        Object.keys(workflow.connections[nodeId]).forEach(outputType => {
            workflow.connections[nodeId][outputType].forEach((conn, index) => {
                conn.forEach(target => {
                    const targetNode = workflow.nodes.find(n => n.id === target.node);
                    console.log(`  → ${targetNode?.name || target.node}`);
                });
            });
        });
    });
}

// Executar
analyzeAndModifyWorkflow();
